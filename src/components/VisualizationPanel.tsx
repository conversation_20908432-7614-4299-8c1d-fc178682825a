import React, { useRef, useEffect } from 'react';
import type { CuttingLayout, PlacedPiece } from '../types';
import { getDimensionsInMm, formatDimension, getUnitSymbol } from '../utils/unitConversion';

interface VisualizationPanelProps {
  layouts: CuttingLayout[];
  selectedLayoutIndex: number;
  onLayoutSelect: (index: number) => void;
}

export const VisualizationPanel: React.FC<VisualizationPanelProps> = ({
  layouts,
  selectedLayoutIndex,
  onLayoutSelect,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (layouts.length > 0 && selectedLayoutIndex >= 0 && selectedLayoutIndex < layouts.length) {
      drawLayout(layouts[selectedLayoutIndex]);
    }
  }, [layouts, selectedLayoutIndex]);

  const drawLayout = (layout: CuttingLayout) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const materialDimensions = getDimensionsInMm(layout.material.dimensions);
    const padding = 20;
    const availableWidth = canvas.width - 2 * padding;
    const availableHeight = canvas.height - 2 * padding;

    // Calculate scale to fit material in canvas
    const scaleX = availableWidth / materialDimensions.length;
    const scaleY = availableHeight / materialDimensions.width;
    const scale = Math.min(scaleX, scaleY);

    const materialWidth = materialDimensions.length * scale;
    const materialHeight = materialDimensions.width * scale;
    const offsetX = (canvas.width - materialWidth) / 2;
    const offsetY = (canvas.height - materialHeight) / 2;

    // Draw material background
    ctx.fillStyle = '#f3f4f6';
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.fillRect(offsetX, offsetY, materialWidth, materialHeight);
    ctx.strokeRect(offsetX, offsetY, materialWidth, materialHeight);

    // Draw material dimensions
    ctx.fillStyle = '#374151';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    
    // Length dimension (top)
    const lengthText = `${formatDimension(layout.material.dimensions.length, layout.material.dimensions.unit)}${getUnitSymbol(layout.material.dimensions.unit)}`;
    ctx.fillText(lengthText, offsetX + materialWidth / 2, offsetY - 5);
    
    // Width dimension (left)
    ctx.save();
    ctx.translate(offsetX - 15, offsetY + materialHeight / 2);
    ctx.rotate(-Math.PI / 2);
    const widthText = `${formatDimension(layout.material.dimensions.width, layout.material.dimensions.unit)}${getUnitSymbol(layout.material.dimensions.unit)}`;
    ctx.fillText(widthText, 0, 0);
    ctx.restore();

    // Draw placed pieces
    layout.placedPieces.forEach((placedPiece, index) => {
      drawPiece(ctx, placedPiece, scale, offsetX, offsetY, index);
    });

    // Draw waste areas (simplified - just show efficiency)
    ctx.fillStyle = '#ef4444';
    ctx.font = '14px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(
      `Efficiency: ${layout.efficiency.toFixed(1)}%`,
      offsetX,
      offsetY + materialHeight + 20
    );
  };

  const drawPiece = (
    ctx: CanvasRenderingContext2D,
    placedPiece: PlacedPiece,
    scale: number,
    offsetX: number,
    offsetY: number,
    index: number
  ) => {
    const piece = placedPiece.piece;
    const pieceDimensions = getDimensionsInMm(piece.dimensions);
    
    const pieceWidth = placedPiece.rotated ? pieceDimensions.width : pieceDimensions.length;
    const pieceHeight = placedPiece.rotated ? pieceDimensions.length : pieceDimensions.width;

    const x = offsetX + placedPiece.x * scale;
    const y = offsetY + placedPiece.y * scale;
    const width = pieceWidth * scale;
    const height = pieceHeight * scale;

    // Draw piece background
    ctx.fillStyle = piece.color || '#3b82f6';
    ctx.globalAlpha = 0.7;
    ctx.fillRect(x, y, width, height);
    ctx.globalAlpha = 1;

    // Draw piece border
    ctx.strokeStyle = '#1f2937';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    // Draw piece label
    ctx.fillStyle = '#ffffff';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(
      piece.name,
      x + width / 2,
      y + height / 2 - 5
    );

    // Draw piece dimensions
    ctx.font = '8px sans-serif';
    const dimensionText = `${formatDimension(piece.dimensions.length, piece.dimensions.unit)} × ${formatDimension(piece.dimensions.width, piece.dimensions.unit)}${getUnitSymbol(piece.dimensions.unit)}`;
    ctx.fillText(
      dimensionText,
      x + width / 2,
      y + height / 2 + 5
    );

    // Draw rotation indicator if rotated
    if (placedPiece.rotated) {
      ctx.fillStyle = '#fbbf24';
      ctx.font = '8px sans-serif';
      ctx.fillText('↻', x + width - 8, y + 10);
    }
  };

  if (layouts.length === 0) {
    return (
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Cutting Layout</h2>
        <div className="text-center py-12 text-gray-500">
          No cutting layouts generated yet. Add materials and project pieces, then run optimization.
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Cutting Layout</h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Layout:</span>
          <select
            value={selectedLayoutIndex}
            onChange={(e) => onLayoutSelect(parseInt(e.target.value))}
            className="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-wood-500"
          >
            {layouts.map((layout, index) => (
              <option key={index} value={index}>
                {index + 1} - {layout.material.name} ({layout.efficiency.toFixed(1)}%)
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="space-y-4">
        {/* Canvas for visualization */}
        <div className="border border-gray-300 rounded-lg overflow-hidden">
          <canvas
            ref={canvasRef}
            width={800}
            height={600}
            className="w-full h-auto max-h-96"
          />
        </div>

        {/* Layout details */}
        {layouts[selectedLayoutIndex] && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-900">Material</h4>
              <p className="text-gray-600">{layouts[selectedLayoutIndex].material.name}</p>
              <p className="text-gray-600">
                {formatDimension(layouts[selectedLayoutIndex].material.dimensions.length, layouts[selectedLayoutIndex].material.dimensions.unit)} × {' '}
                {formatDimension(layouts[selectedLayoutIndex].material.dimensions.width, layouts[selectedLayoutIndex].material.dimensions.unit)}{getUnitSymbol(layouts[selectedLayoutIndex].material.dimensions.unit)}
              </p>
            </div>
            
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-900">Efficiency</h4>
              <p className="text-gray-600">{layouts[selectedLayoutIndex].efficiency.toFixed(1)}%</p>
              <p className="text-gray-600">{layouts[selectedLayoutIndex].placedPieces.length} pieces placed</p>
            </div>
            
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-900">Waste</h4>
              <p className="text-gray-600">
                {(layouts[selectedLayoutIndex].wasteArea / 1000000).toFixed(2)} m²
              </p>
            </div>
          </div>
        )}

        {/* Piece list */}
        {layouts[selectedLayoutIndex] && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Pieces on this layout:</h4>
            <div className="space-y-1">
              {layouts[selectedLayoutIndex].placedPieces.map((placedPiece, index) => (
                <div key={index} className="flex items-center space-x-3 text-sm">
                  <div
                    className="w-3 h-3 rounded"
                    style={{ backgroundColor: placedPiece.piece.color }}
                  />
                  <span className="flex-1">{placedPiece.piece.name}</span>
                  <span className="text-gray-600">
                    {formatDimension(placedPiece.piece.dimensions.length, placedPiece.piece.dimensions.unit)} × {' '}
                    {formatDimension(placedPiece.piece.dimensions.width, placedPiece.piece.dimensions.unit)}{getUnitSymbol(placedPiece.piece.dimensions.unit)}
                  </span>
                  {placedPiece.rotated && (
                    <span className="text-yellow-600 text-xs">Rotated</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
