import React, { useState } from 'react';
import type { Project, ProjectPiece, Unit, GrainDirection } from '../types';
import { Button } from './common/Button';
import { Input } from './common/Input';
import { Modal } from './common/Modal';
import { formatDimension, getUnitSymbol } from '../utils/unitConversion';

interface ProjectManagerProps {
  projects: Project[];
  currentProject: Project | null;
  onCreateProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onSelectProject: (project: Project) => void;
  onUpdateProject: (id: string, project: Partial<Project>) => void;
  onDeleteProject: (id: string) => void;
  onAddPiece: (projectId: string, piece: Omit<ProjectPiece, 'id'>) => void;
  onUpdatePiece: (projectId: string, pieceId: string, piece: Partial<ProjectPiece>) => void;
  onDeletePiece: (projectId: string, pieceId: string) => void;
}

interface ProjectFormData {
  name: string;
  sawKerf: string;
}

interface PieceFormData {
  name: string;
  length: string;
  width: string;
  unit: Unit;
  quantity: string;
  grainDirection: GrainDirection;
}

const initialProjectFormData: ProjectFormData = {
  name: '',
  sawKerf: '3',
};

const initialPieceFormData: PieceFormData = {
  name: '',
  length: '',
  width: '',
  unit: 'mm',
  quantity: '1',
  grainDirection: 'any',
};

export const ProjectManager: React.FC<ProjectManagerProps> = ({
  projects,
  currentProject,
  onCreateProject,
  onSelectProject,
  onUpdateProject,
  onDeleteProject,
  onAddPiece,
  onUpdatePiece,
  onDeletePiece,
}) => {
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [isPieceModalOpen, setIsPieceModalOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingPiece, setEditingPiece] = useState<ProjectPiece | null>(null);
  const [projectFormData, setProjectFormData] = useState<ProjectFormData>(initialProjectFormData);
  const [pieceFormData, setPieceFormData] = useState<PieceFormData>(initialPieceFormData);
  const [projectErrors, setProjectErrors] = useState<Partial<ProjectFormData>>({});
  const [pieceErrors, setPieceErrors] = useState<Partial<PieceFormData>>({});

  const handleOpenProjectModal = (project?: Project) => {
    if (project) {
      setEditingProject(project);
      setProjectFormData({
        name: project.name,
        sawKerf: project.sawKerf.toString(),
      });
    } else {
      setEditingProject(null);
      setProjectFormData(initialProjectFormData);
    }
    setProjectErrors({});
    setIsProjectModalOpen(true);
  };

  const handleCloseProjectModal = () => {
    setIsProjectModalOpen(false);
    setEditingProject(null);
    setProjectFormData(initialProjectFormData);
    setProjectErrors({});
  };

  const handleOpenPieceModal = (piece?: ProjectPiece) => {
    if (piece) {
      setEditingPiece(piece);
      setPieceFormData({
        name: piece.name,
        length: piece.dimensions.length.toString(),
        width: piece.dimensions.width.toString(),
        unit: piece.dimensions.unit,
        quantity: piece.quantity.toString(),
        grainDirection: piece.grainDirection,
      });
    } else {
      setEditingPiece(null);
      setPieceFormData(initialPieceFormData);
    }
    setPieceErrors({});
    setIsPieceModalOpen(true);
  };

  const handleClosePieceModal = () => {
    setIsPieceModalOpen(false);
    setEditingPiece(null);
    setPieceFormData(initialPieceFormData);
    setPieceErrors({});
  };

  const validateProjectForm = (): boolean => {
    const newErrors: Partial<ProjectFormData> = {};

    if (!projectFormData.name.trim()) {
      newErrors.name = 'Project name is required';
    }

    const sawKerf = parseFloat(projectFormData.sawKerf);
    if (!projectFormData.sawKerf || isNaN(sawKerf) || sawKerf < 0) {
      newErrors.sawKerf = 'Valid saw kerf is required';
    }

    setProjectErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePieceForm = (): boolean => {
    const newErrors: Partial<PieceFormData> = {};

    if (!pieceFormData.name.trim()) {
      newErrors.name = 'Piece name is required';
    }

    const length = parseFloat(pieceFormData.length);
    if (!pieceFormData.length || isNaN(length) || length <= 0) {
      newErrors.length = 'Valid length is required';
    }

    const width = parseFloat(pieceFormData.width);
    if (!pieceFormData.width || isNaN(width) || width <= 0) {
      newErrors.width = 'Valid width is required';
    }

    const quantity = parseInt(pieceFormData.quantity);
    if (!pieceFormData.quantity || isNaN(quantity) || quantity <= 0) {
      newErrors.quantity = 'Valid quantity is required';
    }

    setPieceErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProjectSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateProjectForm()) {
      return;
    }

    const projectData = {
      name: projectFormData.name.trim(),
      pieces: editingProject?.pieces || [],
      sawKerf: parseFloat(projectFormData.sawKerf),
    };

    if (editingProject) {
      onUpdateProject(editingProject.id, projectData);
    } else {
      onCreateProject(projectData);
    }

    handleCloseProjectModal();
  };

  const handlePieceSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePieceForm() || !currentProject) {
      return;
    }

    const pieceData = {
      name: pieceFormData.name.trim(),
      dimensions: {
        length: parseFloat(pieceFormData.length),
        width: parseFloat(pieceFormData.width),
        unit: pieceFormData.unit,
      },
      quantity: parseInt(pieceFormData.quantity),
      grainDirection: pieceFormData.grainDirection,
      color: `#${Math.floor(Math.random()*16777215).toString(16)}`, // Random color for visualization
    };

    if (editingPiece) {
      onUpdatePiece(currentProject.id, editingPiece.id, pieceData);
    } else {
      onAddPiece(currentProject.id, pieceData);
    }

    handleClosePieceModal();
  };

  const handleProjectInputChange = (field: keyof ProjectFormData, value: string) => {
    setProjectFormData(prev => ({ ...prev, [field]: value }));
    if (projectErrors[field]) {
      setProjectErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePieceInputChange = (field: keyof PieceFormData, value: string) => {
    setPieceFormData(prev => ({ ...prev, [field]: value }));
    if (pieceErrors[field]) {
      setPieceErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Selection */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Projects</h2>
          <Button onClick={() => handleOpenProjectModal()}>
            New Project
          </Button>
        </div>

        {projects.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No projects created yet. Click "New Project" to get started.
          </div>
        ) : (
          <div className="space-y-2">
            {projects.map((project) => (
              <div
                key={project.id}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  currentProject?.id === project.id
                    ? 'border-wood-500 bg-wood-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onSelectProject(project)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900">{project.name}</h3>
                    <p className="text-sm text-gray-600">
                      {project.pieces.length} pieces • Saw kerf: {project.sawKerf}mm
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenProjectModal(project);
                      }}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteProject(project.id);
                      }}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Current Project Pieces */}
      {currentProject && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {currentProject.name} - Pieces
            </h3>
            <Button onClick={() => handleOpenPieceModal()}>
              Add Piece
            </Button>
          </div>

          {currentProject.pieces.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No pieces added yet. Click "Add Piece" to start defining your cuts.
            </div>
          ) : (
            <div className="space-y-3">
              {currentProject.pieces.map((piece) => (
                <div key={piece.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-4 h-4 rounded"
                          style={{ backgroundColor: piece.color }}
                        />
                        <div>
                          <h4 className="font-medium text-gray-900">{piece.name}</h4>
                          <p className="text-sm text-gray-600">
                            {formatDimension(piece.dimensions.length, piece.dimensions.unit)} × {' '}
                            {formatDimension(piece.dimensions.width, piece.dimensions.unit)}{getUnitSymbol(piece.dimensions.unit)}
                            {' '} • Qty: {piece.quantity}
                            {piece.grainDirection !== 'any' && ` • Grain: ${piece.grainDirection}`}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => handleOpenPieceModal(piece)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => onDeletePiece(currentProject.id, piece.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Project Modal */}
      <Modal
        isOpen={isProjectModalOpen}
        onClose={handleCloseProjectModal}
        title={editingProject ? 'Edit Project' : 'Create New Project'}
        size="md"
      >
        <form onSubmit={handleProjectSubmit} className="space-y-4">
          <Input
            label="Project Name"
            value={projectFormData.name}
            onChange={(e) => handleProjectInputChange('name', e.target.value)}
            error={projectErrors.name}
            placeholder="e.g., Kitchen Cabinet Doors"
            autoFocus
          />

          <Input
            label="Saw Kerf (mm)"
            type="number"
            step="0.1"
            value={projectFormData.sawKerf}
            onChange={(e) => handleProjectInputChange('sawKerf', e.target.value)}
            error={projectErrors.sawKerf}
            helperText="Thickness of your saw blade"
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="secondary" type="button" onClick={handleCloseProjectModal}>
              Cancel
            </Button>
            <Button type="submit">
              {editingProject ? 'Update' : 'Create'} Project
            </Button>
          </div>
        </form>
      </Modal>

      {/* Piece Modal */}
      <Modal
        isOpen={isPieceModalOpen}
        onClose={handleClosePieceModal}
        title={editingPiece ? 'Edit Piece' : 'Add New Piece'}
        size="md"
      >
        <form onSubmit={handlePieceSubmit} className="space-y-4">
          <Input
            label="Piece Name"
            value={pieceFormData.name}
            onChange={(e) => handlePieceInputChange('name', e.target.value)}
            error={pieceErrors.name}
            placeholder="e.g., Door Panel, Shelf"
            autoFocus
          />

          <div className="grid grid-cols-3 gap-4">
            <Input
              label="Length"
              type="number"
              step="0.1"
              value={pieceFormData.length}
              onChange={(e) => handlePieceInputChange('length', e.target.value)}
              error={pieceErrors.length}
            />
            <Input
              label="Width"
              type="number"
              step="0.1"
              value={pieceFormData.width}
              onChange={(e) => handlePieceInputChange('width', e.target.value)}
              error={pieceErrors.width}
            />
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Unit</label>
              <select
                value={pieceFormData.unit}
                onChange={(e) => handlePieceInputChange('unit', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-wood-500 focus:border-transparent"
              >
                <option value="mm">mm</option>
                <option value="cm">cm</option>
                <option value="inches">inches</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Quantity"
              type="number"
              min="1"
              value={pieceFormData.quantity}
              onChange={(e) => handlePieceInputChange('quantity', e.target.value)}
              error={pieceErrors.quantity}
            />
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Grain Direction</label>
              <select
                value={pieceFormData.grainDirection}
                onChange={(e) => handlePieceInputChange('grainDirection', e.target.value as GrainDirection)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-wood-500 focus:border-transparent"
              >
                <option value="any">Any direction</option>
                <option value="horizontal">Horizontal</option>
                <option value="vertical">Vertical</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="secondary" type="button" onClick={handleClosePieceModal}>
              Cancel
            </Button>
            <Button type="submit">
              {editingPiece ? 'Update' : 'Add'} Piece
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};
