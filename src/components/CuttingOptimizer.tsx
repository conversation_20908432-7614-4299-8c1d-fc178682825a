import React, { useState } from 'react';
import type { Material, Project, CuttingPlan, OptimizationSettings } from '../types';
import { Button } from './common/Button';
import { Input } from './common/Input';
import { CuttingOptimizer as Optimizer } from '../utils/cuttingAlgorithm';
import { toMillimeters } from '../utils/unitConversion';

interface CuttingOptimizerProps {
  materials: Material[];
  currentProject: Project | null;
  onOptimizationComplete: (plan: CuttingPlan) => void;
}

export const CuttingOptimizerComponent: React.FC<CuttingOptimizerProps> = ({
  materials,
  currentProject,
  onOptimizationComplete,
}) => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [settings, setSettings] = useState<OptimizationSettings>({
    allowRotation: true,
    prioritizeWasteReduction: true,
    maxLayoutsPerMaterial: 3,
    grainDirectionWeight: 0.7,
  });

  const handleOptimize = async () => {
    if (!currentProject || currentProject.pieces.length === 0 || materials.length === 0) {
      return;
    }

    setIsOptimizing(true);

    try {
      // Simulate async operation for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      const optimizer = new Optimizer(settings);
      const sawKerfMm = toMillimeters(currentProject.sawKerf, 'mm'); // Assuming saw kerf is in mm
      const plan = optimizer.optimize(materials, currentProject.pieces, sawKerfMm);
      
      onOptimizationComplete(plan);
    } catch (error) {
      console.error('Optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleSettingChange = (key: keyof OptimizationSettings, value: boolean | number) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const canOptimize = currentProject && currentProject.pieces.length > 0 && materials.length > 0;

  return (
    <div className="card">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Cutting Optimization</h2>

      {!currentProject && (
        <div className="text-center py-8 text-gray-500">
          Please select a project to optimize cutting layouts.
        </div>
      )}

      {currentProject && currentProject.pieces.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          Add pieces to your project to generate cutting layouts.
        </div>
      )}

      {materials.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          Add materials to your inventory to generate cutting layouts.
        </div>
      )}

      {canOptimize && (
        <div className="space-y-6">
          {/* Optimization Settings */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Optimization Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Allow Piece Rotation</label>
                  <p className="text-xs text-gray-500">Allow pieces to be rotated 90° for better fit</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.allowRotation}
                  onChange={(e) => handleSettingChange('allowRotation', e.target.checked)}
                  className="h-4 w-4 text-wood-600 focus:ring-wood-500 border-gray-300 rounded"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">Prioritize Waste Reduction</label>
                  <p className="text-xs text-gray-500">Focus on minimizing material waste over other factors</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.prioritizeWasteReduction}
                  onChange={(e) => handleSettingChange('prioritizeWasteReduction', e.target.checked)}
                  className="h-4 w-4 text-wood-600 focus:ring-wood-500 border-gray-300 rounded"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Max Layouts per Material"
                  type="number"
                  min="1"
                  max="10"
                  value={settings.maxLayoutsPerMaterial.toString()}
                  onChange={(e) => handleSettingChange('maxLayoutsPerMaterial', parseInt(e.target.value) || 1)}
                  helperText="Maximum number of sheets to use per material type"
                />

                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700">
                    Grain Direction Priority
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={settings.grainDirectionWeight}
                    onChange={(e) => handleSettingChange('grainDirectionWeight', parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Low</span>
                    <span>{(settings.grainDirectionWeight * 100).toFixed(0)}%</span>
                    <span>High</span>
                  </div>
                  <p className="text-xs text-gray-500">How strictly to follow grain direction requirements</p>
                </div>
              </div>
            </div>
          </div>

          {/* Project Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Project Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Project:</span>
                <span className="ml-2 font-medium">{currentProject.name}</span>
              </div>
              <div>
                <span className="text-gray-600">Pieces:</span>
                <span className="ml-2 font-medium">
                  {currentProject.pieces.reduce((sum, piece) => sum + piece.quantity, 0)} total
                </span>
              </div>
              <div>
                <span className="text-gray-600">Saw Kerf:</span>
                <span className="ml-2 font-medium">{currentProject.sawKerf}mm</span>
              </div>
              <div>
                <span className="text-gray-600">Available Materials:</span>
                <span className="ml-2 font-medium">{materials.length} types</span>
              </div>
            </div>
          </div>

          {/* Optimize Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleOptimize}
              disabled={isOptimizing}
              size="lg"
              className="px-8"
            >
              {isOptimizing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Optimizing...</span>
                </div>
              ) : (
                'Generate Cutting Plan'
              )}
            </Button>
          </div>

          {/* Quick Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">💡 Optimization Tips</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Larger pieces are placed first for better efficiency</li>
              <li>• Enable rotation for better material utilization</li>
              <li>• Consider grain direction for structural integrity</li>
              <li>• Account for saw kerf in your measurements</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};
