import React from 'react';
import type { CuttingPlan, CuttingLayout } from '../types';
import { Button } from './common/Button';
import { formatDimension, getUnitSymbol, getDimensionsInMm } from '../utils/unitConversion';

interface CuttingInstructionsProps {
  cuttingPlan: CuttingPlan | null;
}

export const CuttingInstructions: React.FC<CuttingInstructionsProps> = ({
  cuttingPlan,
}) => {
  const generateCuttingSteps = (layout: CuttingLayout, layoutIndex: number) => {
    const steps: string[] = [];
    const materialDimensions = getDimensionsInMm(layout.material.dimensions);
    
    steps.push(`Layout ${layoutIndex + 1}: ${layout.material.name}`);
    steps.push(`Material size: ${formatDimension(layout.material.dimensions.length, layout.material.dimensions.unit)} × ${formatDimension(layout.material.dimensions.width, layout.material.dimensions.unit)}${getUnitSymbol(layout.material.dimensions.unit)}`);
    steps.push('');

    // Sort pieces by position for logical cutting sequence
    const sortedPieces = [...layout.placedPieces].sort((a, b) => {
      if (Math.abs(a.y - b.y) < 10) { // Same row
        return a.x - b.x; // Left to right
      }
      return a.y - b.y; // Top to bottom
    });

    sortedPieces.forEach((placedPiece, index) => {
      const piece = placedPiece.piece;
      const pieceDimensions = getDimensionsInMm(piece.dimensions);
      
      const actualLength = placedPiece.rotated ? pieceDimensions.width : pieceDimensions.length;
      const actualWidth = placedPiece.rotated ? pieceDimensions.length : pieceDimensions.width;

      steps.push(`${index + 1}. Cut "${piece.name}"`);
      steps.push(`   Position: ${(placedPiece.x / 10).toFixed(1)}cm from left, ${(placedPiece.y / 10).toFixed(1)}cm from top`);
      steps.push(`   Size: ${formatDimension(actualLength / 10, 'cm')} × ${formatDimension(actualWidth / 10, 'cm')}cm`);
      
      if (placedPiece.rotated) {
        steps.push(`   ⚠️ Piece is rotated 90°`);
      }
      
      if (piece.grainDirection !== 'any') {
        steps.push(`   📏 Grain direction: ${piece.grainDirection}`);
      }
      
      steps.push('');
    });

    return steps;
  };

  const exportInstructions = () => {
    if (!cuttingPlan) return;

    const content = generateExportContent();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cutting-plan.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateExportContent = (): string => {
    if (!cuttingPlan) return '';

    const lines: string[] = [];
    lines.push('CUTTING PLAN');
    lines.push('='.repeat(50));
    lines.push('');
    
    lines.push(`Total Efficiency: ${cuttingPlan.totalEfficiency.toFixed(1)}%`);
    lines.push(`Total Waste: ${(cuttingPlan.totalWaste / 1000000).toFixed(2)} m²`);
    lines.push(`Layouts: ${cuttingPlan.layouts.length}`);
    lines.push('');

    if (cuttingPlan.unplacedPieces.length > 0) {
      lines.push('⚠️ UNPLACED PIECES:');
      cuttingPlan.unplacedPieces.forEach(piece => {
        lines.push(`- ${piece.name}: ${formatDimension(piece.dimensions.length, piece.dimensions.unit)} × ${formatDimension(piece.dimensions.width, piece.dimensions.unit)}${getUnitSymbol(piece.dimensions.unit)} (Qty: ${piece.quantity})`);
      });
      lines.push('');
    }

    cuttingPlan.layouts.forEach((layout, index) => {
      const steps = generateCuttingSteps(layout, index);
      lines.push(...steps);
      lines.push('-'.repeat(30));
      lines.push('');
    });

    lines.push('Generated by Cut Optimizer');
    lines.push(new Date().toLocaleString());

    return lines.join('\n');
  };

  const printInstructions = () => {
    if (!cuttingPlan) return;

    const content = generateExportContent();
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Cutting Plan</title>
            <style>
              body { font-family: monospace; margin: 20px; }
              pre { white-space: pre-wrap; }
            </style>
          </head>
          <body>
            <pre>${content}</pre>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  if (!cuttingPlan) {
    return (
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Cutting Instructions</h2>
        <div className="text-center py-8 text-gray-500">
          Generate a cutting plan to see step-by-step instructions.
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Cutting Instructions</h2>
        <div className="flex space-x-2">
          <Button variant="secondary" onClick={exportInstructions}>
            Export
          </Button>
          <Button variant="secondary" onClick={printInstructions}>
            Print
          </Button>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="font-medium text-gray-900 mb-2">Plan Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Total Efficiency:</span>
            <span className="ml-2 font-medium text-green-600">
              {cuttingPlan.totalEfficiency.toFixed(1)}%
            </span>
          </div>
          <div>
            <span className="text-gray-600">Total Waste:</span>
            <span className="ml-2 font-medium">
              {(cuttingPlan.totalWaste / 1000000).toFixed(2)} m²
            </span>
          </div>
          <div>
            <span className="text-gray-600">Layouts:</span>
            <span className="ml-2 font-medium">{cuttingPlan.layouts.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Materials Used:</span>
            <span className="ml-2 font-medium">
              {new Set(cuttingPlan.layouts.map(l => l.material.id)).size}
            </span>
          </div>
        </div>
      </div>

      {/* Unplaced Pieces Warning */}
      {cuttingPlan.unplacedPieces.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-red-900 mb-2">⚠️ Unplaced Pieces</h3>
          <p className="text-sm text-red-800 mb-2">
            The following pieces could not be placed on available materials:
          </p>
          <ul className="text-sm text-red-800 space-y-1">
            {cuttingPlan.unplacedPieces.map((piece, index) => (
              <li key={index}>
                • {piece.name}: {formatDimension(piece.dimensions.length, piece.dimensions.unit)} × {' '}
                {formatDimension(piece.dimensions.width, piece.dimensions.unit)}{getUnitSymbol(piece.dimensions.unit)} 
                (Qty: {piece.quantity})
              </li>
            ))}
          </ul>
          <p className="text-sm text-red-800 mt-2">
            Consider adding larger materials or reducing piece sizes.
          </p>
        </div>
      )}

      {/* Cutting Steps */}
      <div className="space-y-6">
        {cuttingPlan.layouts.map((layout, layoutIndex) => (
          <div key={layoutIndex} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-900">
                Layout {layoutIndex + 1}: {layout.material.name}
              </h3>
              <span className="text-sm text-gray-600">
                Efficiency: {layout.efficiency.toFixed(1)}%
              </span>
            </div>

            <div className="bg-gray-50 p-3 rounded mb-4">
              <p className="text-sm text-gray-700">
                <strong>Material:</strong> {formatDimension(layout.material.dimensions.length, layout.material.dimensions.unit)} × {' '}
                {formatDimension(layout.material.dimensions.width, layout.material.dimensions.unit)}{getUnitSymbol(layout.material.dimensions.unit)}
              </p>
              <p className="text-sm text-gray-700">
                <strong>Pieces:</strong> {layout.placedPieces.length}
              </p>
            </div>

            <div className="space-y-3">
              {layout.placedPieces
                .sort((a, b) => {
                  if (Math.abs(a.y - b.y) < 10) return a.x - b.x;
                  return a.y - b.y;
                })
                .map((placedPiece, pieceIndex) => {
                  const piece = placedPiece.piece;
                  const pieceDimensions = getDimensionsInMm(piece.dimensions);
                  const actualLength = placedPiece.rotated ? pieceDimensions.width : pieceDimensions.length;
                  const actualWidth = placedPiece.rotated ? pieceDimensions.length : pieceDimensions.width;

                  return (
                    <div key={pieceIndex} className="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded">
                      <div className="flex-shrink-0 w-6 h-6 bg-wood-600 text-white text-xs rounded flex items-center justify-center">
                        {pieceIndex + 1}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <div
                            className="w-3 h-3 rounded"
                            style={{ backgroundColor: piece.color }}
                          />
                          <span className="font-medium">{piece.name}</span>
                          {placedPiece.rotated && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                              Rotated 90°
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>
                            <strong>Position:</strong> {(placedPiece.x / 10).toFixed(1)}cm from left, {(placedPiece.y / 10).toFixed(1)}cm from top
                          </p>
                          <p>
                            <strong>Cut size:</strong> {formatDimension(actualLength / 10, 'cm')} × {formatDimension(actualWidth / 10, 'cm')}cm
                          </p>
                          {piece.grainDirection !== 'any' && (
                            <p>
                              <strong>Grain direction:</strong> {piece.grainDirection}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
