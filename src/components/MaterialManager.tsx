import React, { useState } from 'react';
import type { Material, Unit } from '../types';
import { Button } from './common/Button';
import { Input } from './common/Input';
import { Modal } from './common/Modal';
import { formatDimension, getUnitSymbol } from '../utils/unitConversion';

interface MaterialManagerProps {
  materials: Material[];
  onAddMaterial: (material: Omit<Material, 'id' | 'createdAt'>) => void;
  onUpdateMaterial: (id: string, material: Partial<Material>) => void;
  onDeleteMaterial: (id: string) => void;
}

interface MaterialFormData {
  name: string;
  length: string;
  width: string;
  unit: Unit;
  quantity: string;
  thickness: string;
  materialType: string;
}

const initialFormData: MaterialFormData = {
  name: '',
  length: '',
  width: '',
  unit: 'mm',
  quantity: '1',
  thickness: '',
  materialType: '',
};

export const MaterialManager: React.FC<MaterialManagerProps> = ({
  materials,
  onAddMaterial,
  onUpdateMaterial,
  onDeleteMaterial,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);
  const [formData, setFormData] = useState<MaterialFormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<MaterialFormData>>({});

  const handleOpenModal = (material?: Material) => {
    if (material) {
      setEditingMaterial(material);
      setFormData({
        name: material.name,
        length: material.dimensions.length.toString(),
        width: material.dimensions.width.toString(),
        unit: material.dimensions.unit,
        quantity: material.quantity.toString(),
        thickness: material.thickness?.toString() || '',
        materialType: material.materialType || '',
      });
    } else {
      setEditingMaterial(null);
      setFormData(initialFormData);
    }
    setErrors({});
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingMaterial(null);
    setFormData(initialFormData);
    setErrors({});
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<MaterialFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    const length = parseFloat(formData.length);
    if (!formData.length || isNaN(length) || length <= 0) {
      newErrors.length = 'Valid length is required';
    }

    const width = parseFloat(formData.width);
    if (!formData.width || isNaN(width) || width <= 0) {
      newErrors.width = 'Valid width is required';
    }

    const quantity = parseInt(formData.quantity);
    if (!formData.quantity || isNaN(quantity) || quantity <= 0) {
      newErrors.quantity = 'Valid quantity is required';
    }

    if (formData.thickness) {
      const thickness = parseFloat(formData.thickness);
      if (isNaN(thickness) || thickness <= 0) {
        newErrors.thickness = 'Valid thickness is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const materialData = {
      name: formData.name.trim(),
      dimensions: {
        length: parseFloat(formData.length),
        width: parseFloat(formData.width),
        unit: formData.unit,
      },
      quantity: parseInt(formData.quantity),
      thickness: formData.thickness ? parseFloat(formData.thickness) : undefined,
      materialType: formData.materialType.trim() || undefined,
    };

    if (editingMaterial) {
      onUpdateMaterial(editingMaterial.id, materialData);
    } else {
      onAddMaterial(materialData);
    }

    handleCloseModal();
  };

  const handleInputChange = (field: keyof MaterialFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Material Inventory</h2>
        <Button onClick={() => handleOpenModal()}>
          Add Material
        </Button>
      </div>

      {materials.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No materials added yet. Click "Add Material" to get started.
        </div>
      ) : (
        <div className="space-y-4">
          {materials.map((material) => (
            <div key={material.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{material.name}</h3>
                  <p className="text-sm text-gray-600">
                    {formatDimension(material.dimensions.length, material.dimensions.unit)} × {' '}
                    {formatDimension(material.dimensions.width, material.dimensions.unit)}{getUnitSymbol(material.dimensions.unit)}
                    {material.thickness && ` × ${formatDimension(material.thickness, material.dimensions.unit)}${getUnitSymbol(material.dimensions.unit)} thick`}
                  </p>
                  <p className="text-sm text-gray-600">
                    Quantity: {material.quantity}
                    {material.materialType && ` • Type: ${material.materialType}`}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleOpenModal(material)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() => onDeleteMaterial(material.id)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingMaterial ? 'Edit Material' : 'Add New Material'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Material Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="e.g., Plywood 18mm"
            autoFocus
          />

          <div className="grid grid-cols-3 gap-4">
            <Input
              label="Length"
              type="number"
              step="0.1"
              value={formData.length}
              onChange={(e) => handleInputChange('length', e.target.value)}
              error={errors.length}
            />
            <Input
              label="Width"
              type="number"
              step="0.1"
              value={formData.width}
              onChange={(e) => handleInputChange('width', e.target.value)}
              error={errors.width}
            />
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">Unit</label>
              <select
                value={formData.unit}
                onChange={(e) => handleInputChange('unit', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-wood-500 focus:border-transparent"
              >
                <option value="mm">mm</option>
                <option value="cm">cm</option>
                <option value="inches">inches</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Quantity"
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', e.target.value)}
              error={errors.quantity}
            />
            <Input
              label="Thickness (optional)"
              type="number"
              step="0.1"
              value={formData.thickness}
              onChange={(e) => handleInputChange('thickness', e.target.value)}
              error={errors.thickness}
            />
          </div>

          <Input
            label="Material Type (optional)"
            value={formData.materialType}
            onChange={(e) => handleInputChange('materialType', e.target.value)}
            placeholder="e.g., Plywood, MDF, Hardwood"
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="secondary" type="button" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button type="submit">
              {editingMaterial ? 'Update' : 'Add'} Material
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};
