import type { CuttingPlan, CuttingLayout } from '../types';
import { formatDimension, getUnitSymbol, getDimensionsInMm } from './unitConversion';

export const exportCuttingPlanAsText = (cuttingPlan: CuttingPlan): string => {
  const lines: string[] = [];
  
  lines.push('CUTTING PLAN');
  lines.push('='.repeat(50));
  lines.push('');
  
  // Summary
  lines.push(`Total Efficiency: ${cuttingPlan.totalEfficiency.toFixed(1)}%`);
  lines.push(`Total Waste: ${(cuttingPlan.totalWaste / 1000000).toFixed(2)} m²`);
  lines.push(`Number of Layouts: ${cuttingPlan.layouts.length}`);
  lines.push('');

  // Unplaced pieces warning
  if (cuttingPlan.unplacedPieces.length > 0) {
    lines.push('⚠️ UNPLACED PIECES:');
    cuttingPlan.unplacedPieces.forEach(piece => {
      lines.push(`- ${piece.name}: ${formatDimension(piece.dimensions.length, piece.dimensions.unit)} × ${formatDimension(piece.dimensions.width, piece.dimensions.unit)}${getUnitSymbol(piece.dimensions.unit)} (Qty: ${piece.quantity})`);
    });
    lines.push('');
  }

  // Layout details
  cuttingPlan.layouts.forEach((layout, layoutIndex) => {
    lines.push(`LAYOUT ${layoutIndex + 1}: ${layout.material.name}`);
    lines.push('-'.repeat(30));
    lines.push(`Material Size: ${formatDimension(layout.material.dimensions.length, layout.material.dimensions.unit)} × ${formatDimension(layout.material.dimensions.width, layout.material.dimensions.unit)}${getUnitSymbol(layout.material.dimensions.unit)}`);
    lines.push(`Efficiency: ${layout.efficiency.toFixed(1)}%`);
    lines.push(`Pieces: ${layout.placedPieces.length}`);
    lines.push('');

    // Cutting instructions
    const sortedPieces = [...layout.placedPieces].sort((a, b) => {
      if (Math.abs(a.y - b.y) < 10) return a.x - b.x;
      return a.y - b.y;
    });

    sortedPieces.forEach((placedPiece, pieceIndex) => {
      const piece = placedPiece.piece;
      const pieceDimensions = getDimensionsInMm(piece.dimensions);
      const actualLength = placedPiece.rotated ? pieceDimensions.width : pieceDimensions.length;
      const actualWidth = placedPiece.rotated ? pieceDimensions.length : pieceDimensions.width;

      lines.push(`${pieceIndex + 1}. ${piece.name}`);
      lines.push(`   Position: ${(placedPiece.x / 10).toFixed(1)}cm from left, ${(placedPiece.y / 10).toFixed(1)}cm from top`);
      lines.push(`   Size: ${formatDimension(actualLength / 10, 'cm')} × ${formatDimension(actualWidth / 10, 'cm')}cm`);
      
      if (placedPiece.rotated) {
        lines.push(`   ⚠️ Piece is rotated 90°`);
      }
      
      if (piece.grainDirection !== 'any') {
        lines.push(`   📏 Grain direction: ${piece.grainDirection}`);
      }
      
      lines.push('');
    });

    lines.push('');
  });

  lines.push('Generated by Cut Optimizer');
  lines.push(new Date().toLocaleString());

  return lines.join('\n');
};

export const exportCuttingPlanAsCSV = (cuttingPlan: CuttingPlan): string => {
  const lines: string[] = [];
  
  // Header
  lines.push('Layout,Material,Piece Name,Position X (cm),Position Y (cm),Length (cm),Width (cm),Rotated,Grain Direction');

  cuttingPlan.layouts.forEach((layout, layoutIndex) => {
    layout.placedPieces.forEach((placedPiece) => {
      const piece = placedPiece.piece;
      const pieceDimensions = getDimensionsInMm(piece.dimensions);
      const actualLength = placedPiece.rotated ? pieceDimensions.width : pieceDimensions.length;
      const actualWidth = placedPiece.rotated ? pieceDimensions.length : pieceDimensions.width;

      const row = [
        layoutIndex + 1,
        `"${layout.material.name}"`,
        `"${piece.name}"`,
        (placedPiece.x / 10).toFixed(1),
        (placedPiece.y / 10).toFixed(1),
        (actualLength / 10).toFixed(1),
        (actualWidth / 10).toFixed(1),
        placedPiece.rotated ? 'Yes' : 'No',
        piece.grainDirection
      ];
      
      lines.push(row.join(','));
    });
  });

  return lines.join('\n');
};

export const downloadFile = (content: string, filename: string, mimeType: string = 'text/plain') => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

export const printCuttingPlan = (cuttingPlan: CuttingPlan) => {
  const content = exportCuttingPlanAsText(cuttingPlan);
  const printWindow = window.open('', '_blank');
  
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>Cutting Plan</title>
          <style>
            body { 
              font-family: 'Courier New', monospace; 
              margin: 20px; 
              line-height: 1.4;
            }
            pre { 
              white-space: pre-wrap; 
              font-size: 12px;
            }
            @media print {
              body { margin: 0; }
              pre { font-size: 10px; }
            }
          </style>
        </head>
        <body>
          <pre>${content}</pre>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  }
};
