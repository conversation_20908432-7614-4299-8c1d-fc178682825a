import type {
  Material,
  ProjectPiece,
  CuttingPlan,
  CuttingLayout,
  PlacedPiece,
  OptimizationSettings,
} from '../types';
import { getDimensionsInMm } from './unitConversion';

interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface PieceWithDimensions extends ProjectPiece {
  lengthMm: number;
  widthMm: number;
}

export class CuttingOptimizer {
  private settings: OptimizationSettings;

  constructor(settings: OptimizationSettings) {
    this.settings = settings;
  }

  optimize(materials: Material[], pieces: ProjectPiece[], sawKerfMm: number): CuttingPlan {
    // Convert all pieces to mm for calculations
    const piecesWithDimensions = this.preparePieces(pieces);
    
    // Sort materials by area (largest first)
    const sortedMaterials = [...materials].sort((a, b) => {
      const aArea = this.getMaterialArea(a);
      const bArea = this.getMaterialArea(b);
      return bArea - aArea;
    });

    const layouts: CuttingLayout[] = [];
    const remainingPieces = [...piecesWithDimensions];

    // Try to place pieces on each material
    for (const material of sortedMaterials) {
      for (let i = 0; i < material.quantity; i++) {
        const layout = this.createLayoutForMaterial(material, remainingPieces, sawKerfMm);
        if (layout.placedPieces.length > 0) {
          layouts.push(layout);
          // Remove placed pieces from remaining pieces
          layout.placedPieces.forEach(placedPiece => {
            const pieceIndex = remainingPieces.findIndex(p => p.id === placedPiece.piece.id);
            if (pieceIndex !== -1) {
              if (remainingPieces[pieceIndex].quantity > 1) {
                remainingPieces[pieceIndex].quantity--;
              } else {
                remainingPieces.splice(pieceIndex, 1);
              }
            }
          });
        }
        
        if (remainingPieces.length === 0) break;
      }
      
      if (remainingPieces.length === 0) break;
    }

    return this.createCuttingPlan(layouts, remainingPieces);
  }

  private preparePieces(pieces: ProjectPiece[]): PieceWithDimensions[] {
    const result: PieceWithDimensions[] = [];
    
    pieces.forEach(piece => {
      const dimensions = getDimensionsInMm(piece.dimensions);
      for (let i = 0; i < piece.quantity; i++) {
        result.push({
          ...piece,
          id: `${piece.id}_${i}`,
          quantity: 1,
          lengthMm: dimensions.length,
          widthMm: dimensions.width,
        });
      }
    });

    // Sort by area (largest first) for better packing
    return result.sort((a, b) => (b.lengthMm * b.widthMm) - (a.lengthMm * a.widthMm));
  }

  private getMaterialArea(material: Material): number {
    const dimensions = getDimensionsInMm(material.dimensions);
    return dimensions.length * dimensions.width;
  }

  private createLayoutForMaterial(
    material: Material,
    pieces: PieceWithDimensions[],
    sawKerfMm: number
  ): CuttingLayout {
    const materialDimensions = getDimensionsInMm(material.dimensions);
    const placedPieces: PlacedPiece[] = [];
    const occupiedRectangles: Rectangle[] = [];

    // Try to place each piece
    for (const piece of pieces) {
      if (piece.quantity === 0) continue;

      const placement = this.findBestPlacement(
        piece,
        materialDimensions,
        occupiedRectangles,
        sawKerfMm
      );

      if (placement) {
        placedPieces.push(placement);
        
        // Add the occupied rectangle (including kerf)
        const rect: Rectangle = {
          x: placement.x,
          y: placement.y,
          width: placement.rotated ? piece.widthMm + sawKerfMm : piece.lengthMm + sawKerfMm,
          height: placement.rotated ? piece.lengthMm + sawKerfMm : piece.widthMm + sawKerfMm,
        };
        occupiedRectangles.push(rect);
      }
    }

    const usedArea = placedPieces.reduce((sum, placed) => {
      const piece = placed.piece as PieceWithDimensions;
      return sum + (piece.lengthMm * piece.widthMm);
    }, 0);

    const totalArea = materialDimensions.length * materialDimensions.width;
    const efficiency = (usedArea / totalArea) * 100;

    return {
      material,
      placedPieces,
      wasteArea: totalArea - usedArea,
      efficiency,
    };
  }

  private findBestPlacement(
    piece: PieceWithDimensions,
    materialDimensions: { length: number; width: number },
    occupiedRectangles: Rectangle[],
    sawKerfMm: number
  ): PlacedPiece | null {
    const orientations = this.getValidOrientations(piece);
    
    for (const rotated of orientations) {
      const pieceWidth = rotated ? piece.widthMm : piece.lengthMm;
      const pieceHeight = rotated ? piece.lengthMm : piece.widthMm;

      // Try bottom-left fill algorithm
      for (let y = 0; y <= materialDimensions.width - pieceHeight; y += 1) {
        for (let x = 0; x <= materialDimensions.length - pieceWidth; x += 1) {
          const testRect: Rectangle = {
            x,
            y,
            width: pieceWidth + sawKerfMm,
            height: pieceHeight + sawKerfMm,
          };

          if (!this.rectangleOverlaps(testRect, occupiedRectangles)) {
            return {
              piece,
              x,
              y,
              rotated,
            };
          }
        }
      }
    }

    return null;
  }

  private getValidOrientations(piece: PieceWithDimensions): boolean[] {
    const orientations = [false]; // Original orientation

    if (this.settings.allowRotation) {
      // Check if rotation is allowed based on grain direction
      if (piece.grainDirection === 'any') {
        orientations.push(true);
      } else if (this.settings.grainDirectionWeight < 0.8) {
        // Allow rotation if grain direction weight is low
        orientations.push(true);
      }
    }

    return orientations;
  }

  private rectangleOverlaps(rect: Rectangle, rectangles: Rectangle[]): boolean {
    return rectangles.some(other => 
      rect.x < other.x + other.width &&
      rect.x + rect.width > other.x &&
      rect.y < other.y + other.height &&
      rect.y + rect.height > other.y
    );
  }

  private createCuttingPlan(layouts: CuttingLayout[], unplacedPieces: PieceWithDimensions[]): CuttingPlan {
    const totalWaste = layouts.reduce((sum, layout) => sum + layout.wasteArea, 0);
    const totalArea = layouts.reduce((sum, layout) => {
      const materialDimensions = getDimensionsInMm(layout.material.dimensions);
      return sum + (materialDimensions.length * materialDimensions.width);
    }, 0);
    
    const totalEfficiency = totalArea > 0 ? ((totalArea - totalWaste) / totalArea) * 100 : 0;

    // Convert unplaced pieces back to original format
    const unplacedOriginal: ProjectPiece[] = [];
    const pieceGroups = new Map<string, number>();
    
    unplacedPieces.forEach(piece => {
      const originalId = piece.id.split('_')[0];
      pieceGroups.set(originalId, (pieceGroups.get(originalId) || 0) + 1);
    });

    pieceGroups.forEach((quantity, originalId) => {
      const originalPiece = unplacedPieces.find(p => p.id.startsWith(originalId));
      if (originalPiece) {
        unplacedOriginal.push({
          ...originalPiece,
          id: originalId,
          quantity,
        });
      }
    });

    return {
      layouts,
      totalWaste,
      totalEfficiency,
      unplacedPieces: unplacedOriginal,
    };
  }
}
