import type { Unit, Dimensions } from '../types';

// Convert all measurements to millimeters for internal calculations
export const toMillimeters = (value: number, unit: Unit): number => {
  switch (unit) {
    case 'mm':
      return value;
    case 'cm':
      return value * 10;
    case 'inches':
      return value * 25.4;
    default:
      return value;
  }
};

// Convert from millimeters to target unit
export const fromMillimeters = (value: number, unit: Unit): number => {
  switch (unit) {
    case 'mm':
      return value;
    case 'cm':
      return value / 10;
    case 'inches':
      return value / 25.4;
    default:
      return value;
  }
};

// Convert dimensions to a target unit
export const convertDimensions = (dimensions: Dimensions, targetUnit: Unit): Dimensions => {
  if (dimensions.unit === targetUnit) {
    return dimensions;
  }

  const lengthInMm = toMillimeters(dimensions.length, dimensions.unit);
  const widthInMm = toMillimeters(dimensions.width, dimensions.unit);

  return {
    length: fromMillimeters(lengthInMm, targetUnit),
    width: fromMillimeters(widthInMm, targetUnit),
    unit: targetUnit,
  };
};

// Get dimensions in millimeters for calculations
export const getDimensionsInMm = (dimensions: Dimensions): { length: number; width: number } => {
  return {
    length: toMillimeters(dimensions.length, dimensions.unit),
    width: toMillimeters(dimensions.width, dimensions.unit),
  };
};

// Format number for display with appropriate precision
export const formatDimension = (value: number, unit: Unit): string => {
  const precision = unit === 'inches' ? 3 : unit === 'cm' ? 1 : 0;
  return value.toFixed(precision);
};

// Get unit symbol for display
export const getUnitSymbol = (unit: Unit): string => {
  switch (unit) {
    case 'mm':
      return 'mm';
    case 'cm':
      return 'cm';
    case 'inches':
      return '"';
    default:
      return unit;
  }
};
