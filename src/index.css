/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: system-ui, -apple-system, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.5;
}

/* Utility classes */
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-white { background-color: #ffffff; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-red-50 { background-color: #fef2f2; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-yellow-100 { background-color: #fef3c7; }

.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-white { color: #ffffff; }
.text-red-600 { color: #dc2626; }
.text-red-800 { color: #991b1b; }
.text-red-900 { color: #7f1d1d; }
.text-blue-800 { color: #1e40af; }
.text-blue-900 { color: #1e3a8a; }
.text-yellow-600 { color: #d97706; }
.text-yellow-800 { color: #92400e; }
.text-green-600 { color: #059669; }

.border { border: 1px solid #e5e7eb; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-red-200 { border-color: #fecaca; }
.border-blue-200 { border-color: #bfdbfe; }
.border-b { border-bottom: 1px solid #e5e7eb; }
.border-b-2 { border-bottom: 2px solid; }
.border-transparent { border-color: transparent; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }

.p-1 { padding: 0.25rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }

.m-2 { margin: 0.5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.ml-2 { margin-left: 0.5rem; }
.mt-2 { margin-top: 0.5rem; }

.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-6 { width: 1.5rem; }
.w-full { width: 100%; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-16 { height: 4rem; }
.h-auto { height: auto; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-7xl { max-width: 80rem; }
.max-h-96 { max-height: 24rem; }

.flex { display: flex; }
.inline-block { display: inline-block; }
.block { display: block; }
.grid { display: grid; }
.hidden { display: none; }

.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }

.text-center { text-align: center; }
.text-left { text-align: left; }

.cursor-pointer { cursor: pointer; }

.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }

.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Component styles */
.card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.btn-primary {
  background-color: #a18072;
  color: #ffffff;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #977669;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #1f2937;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.btn-danger {
  background-color: #dc2626;
  color: #ffffff;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
}

.input-field:focus {
  border-color: #a18072;
  box-shadow: 0 0 0 2px rgba(161, 128, 114, 0.2);
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\\:block { display: block; }
  .sm\\:p-0 { padding: 0; }
}

@media (min-width: 768px) {
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

/* Animation */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Fixed positioning */
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-50 { z-index: 50; }

/* Additional utility classes */
.pt-4 { padding-top: 1rem; }
.pb-20 { padding-bottom: 5rem; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }
.align-middle { vertical-align: middle; }
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.leading-6 { line-height: 1.5rem; }
.min-h-screen { min-height: 100vh; }

/* Button size variants */
.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

/* Focus styles */
.focus\\:outline-none:focus { outline: none; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(161, 128, 114, 0.2); }
.focus\\:ring-wood-500:focus { box-shadow: 0 0 0 2px rgba(161, 128, 114, 0.2); }
.focus\\:ring-red-500:focus { box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2); }
.focus\\:ring-gray-500:focus { box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2); }
.focus\\:ring-offset-2:focus { box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(161, 128, 114, 0.2); }
.focus\\:border-transparent:focus { border-color: transparent; }

/* Hover states for borders */
.hover\\:border-gray-300:hover { border-color: #d1d5db; }
.hover\\:text-gray-700:hover { color: #374151; }

/* Active states for tabs */
.border-wood-500 { border-color: #a18072; }
.text-wood-600 { color: #a18072; }

/* Additional background colors */
.bg-wood-50 { background-color: #fdf8f6; }
.bg-wood-600 { background-color: #a18072; }

/* Select styling */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Checkbox styling */
input[type="checkbox"] {
  appearance: none;
  background-color: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  width: 1rem;
  height: 1rem;
  position: relative;
  cursor: pointer;
}

input[type="checkbox"]:checked {
  background-color: #a18072;
  border-color: #a18072;
}

input[type="checkbox"]:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Range input styling */
input[type="range"] {
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 0.5rem;
  border-radius: 0.25rem;
}

input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  background: #a18072;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 0.5rem;
  border-radius: 0.25rem;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: #a18072;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Modal improvements */
.modal-backdrop {
  backdrop-filter: blur(2px);
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Smooth focus transitions */
input:focus,
select:focus,
textarea:focus {
  transition: box-shadow 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Modal animation improvements */
.modal-enter {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}
