# Cut Optimizer - Woodworking Cutting Layout Optimizer

A comprehensive web application for optimizing cutting layouts in woodworking projects. This tool helps woodworkers minimize material waste and generate efficient cutting plans.

## Features

### 🪚 Material Management
- Add and manage inventory of base materials
- Support for multiple units (mm, cm, inches)
- Track material dimensions, quantity, and properties
- Optional material type and thickness specification

### 📋 Project Management
- Create and manage multiple woodworking projects
- Define required pieces with dimensions and quantities
- Set grain direction requirements for each piece
- Visual color coding for easy identification

### ⚡ Cutting Optimization
- Advanced 2D bin packing algorithm
- Minimize material waste through intelligent placement
- Account for saw kerf (blade thickness)
- Respect grain direction constraints
- Configurable optimization settings

### 👁️ Visual Layout
- Interactive cutting diagrams with HTML5 Canvas
- Color-coded pieces with clear labeling
- Dimension display and rotation indicators
- Multiple layout views with efficiency metrics

### 📝 Cutting Instructions
- Step-by-step cutting sequences
- Detailed measurements and positions
- Export to text or CSV formats
- Print-friendly cutting plans

## Technology Stack

- **Frontend**: React 19 with TypeScript
- **Build Tool**: Vite 6
- **Styling**: Custom CSS (Tailwind-inspired utility classes)
- **State Management**: React hooks with localStorage persistence
- **Algorithms**: Custom 2D bin packing optimization

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd cut-optimizer
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

## Usage Guide

### 1. Add Materials
- Navigate to the "Materials" tab
- Click "Add Material" to define your available stock
- Enter dimensions, quantity, and optional properties

### 2. Create a Project
- Go to the "Projects" tab
- Click "New Project" and give it a name
- Set the saw kerf (blade thickness) for accurate calculations

### 3. Define Pieces
- Add pieces to your project with dimensions and quantities
- Specify grain direction requirements if needed
- Each piece gets a unique color for visualization

### 4. Optimize Layout
- Switch to the "Optimize" tab
- Configure optimization settings:
  - Allow piece rotation
  - Prioritize waste reduction
  - Set grain direction importance
- Click "Generate Cutting Plan"

### 5. View Results
- Check the "Visualize" tab for interactive cutting diagrams
- Review efficiency metrics and piece placement
- Switch between different material layouts

### 6. Get Instructions
- Visit the "Instructions" tab for detailed cutting steps
- Export or print the cutting plan
- Follow the step-by-step sequence for optimal results

## Optimization Algorithm

The application uses a sophisticated 2D bin packing algorithm that:

- Sorts pieces by area (largest first) for better packing efficiency
- Uses bottom-left fill strategy for optimal placement
- Considers saw kerf spacing between pieces
- Respects grain direction constraints when specified
- Allows configurable piece rotation for better utilization
- Calculates waste areas and efficiency metrics

## Browser Compatibility

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+

## Contributing

This is a demonstration project showcasing modern web development practices for woodworking applications. Feel free to fork and enhance!

## License

MIT License - feel free to use this code for your own projects.
